#!/usr/bin/env python3
"""
Test runner script for UR robot functionality tests.

This script provides convenient options for running different subsets of tests
based on whether a robot is available or not.
"""

import argparse
import subprocess
import sys
import socket
from pathlib import Path


def check_robot_connection(robot_ip="***************", port=30001, timeout=2.0):
    """
    Check if robot is available for connection.
    
    Args:
        robot_ip: IP address of the robot
        port: Port to test connection on
        timeout: Connection timeout in seconds
        
    Returns:
        True if connection successful, False otherwise
    """
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as test_socket:
            test_socket.settimeout(timeout)
            test_socket.connect((robot_ip, port))
            return True
    except (socket.error, socket.timeout):
        return False


def run_pytest(args_list):
    """Run pytest with given arguments."""
    cmd = ["python", "-m", "pytest"] + args_list
    print(f"Running: {' '.join(cmd)}")
    return subprocess.run(cmd, cwd=Path(__file__).parent)


def main():
    parser = argparse.ArgumentParser(
        description="Run UR robot tests with various options",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python run_tests.py --all                    # Run all tests
  python run_tests.py --mock-only              # Run only mock tests (no robot)
  python run_tests.py --robot-only             # Run only robot tests
  python run_tests.py --connection             # Run only connection tests
  python run_tests.py --parsing                # Run only data parsing tests
  python run_tests.py --movement               # Run only movement tests
  python run_tests.py --check-robot            # Just check robot availability
        """
    )
    
    # Test selection options
    test_group = parser.add_mutually_exclusive_group()
    test_group.add_argument(
        "--all", action="store_true",
        help="Run all tests (default)"
    )
    test_group.add_argument(
        "--mock-only", action="store_true",
        help="Run only mock tests (no robot required)"
    )
    test_group.add_argument(
        "--robot-only", action="store_true",
        help="Run only tests requiring actual robot"
    )
    test_group.add_argument(
        "--connection", action="store_true",
        help="Run only connection tests"
    )
    test_group.add_argument(
        "--parsing", action="store_true",
        help="Run only data parsing tests"
    )
    test_group.add_argument(
        "--movement", action="store_true",
        help="Run only movement tests (cartesian and joint)"
    )
    test_group.add_argument(
        "--cartesian", action="store_true",
        help="Run only cartesian movement tests"
    )
    test_group.add_argument(
        "--joint", action="store_true",
        help="Run only joint movement tests"
    )
    test_group.add_argument(
        "--utilities", action="store_true",
        help="Run only utility function tests"
    )
    
    # Other options
    parser.add_argument(
        "--check-robot", action="store_true",
        help="Check robot availability and exit"
    )
    parser.add_argument(
        "--robot-ip", default="***************",
        help="Robot IP address (default: ***************)"
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true",
        help="Verbose output"
    )
    parser.add_argument(
        "--quiet", "-q", action="store_true",
        help="Quiet output"
    )
    parser.add_argument(
        "--failfast", "-x", action="store_true",
        help="Stop on first failure"
    )
    
    args = parser.parse_args()
    
    # Check robot availability if requested
    if args.check_robot:
        print(f"Checking robot availability at {args.robot_ip}...")
        if check_robot_connection(args.robot_ip):
            print("✓ Robot is available")
            return 0
        else:
            print("✗ Robot is not available")
            return 1
    
    # Build pytest arguments
    pytest_args = ["tests/test_ur_robot.py"]
    
    # Add verbosity options
    if args.verbose:
        pytest_args.append("-v")
    elif args.quiet:
        pytest_args.append("-q")
    
    # Add fail-fast option
    if args.failfast:
        pytest_args.append("-x")
    
    # Add test selection
    if args.mock_only:
        pytest_args.extend(["-m", "not urrobot"])
        print("Running mock tests only (no robot required)")
    elif args.robot_only:
        pytest_args.extend(["-m", "urrobot"])
        print("Running robot-dependent tests only")
        # Check robot availability
        if not check_robot_connection(args.robot_ip):
            print(f"Warning: Robot at {args.robot_ip} is not available. Tests may be skipped.")
    elif args.connection:
        pytest_args.append("tests/test_ur_robot.py::TestURRobotConnection")
        print("Running connection tests only")
    elif args.parsing:
        pytest_args.append("tests/test_ur_robot.py::TestURRobotDataParsing")
        print("Running data parsing tests only")
    elif args.movement:
        pytest_args.extend([
            "tests/test_ur_robot.py::TestURRobotCartesianMovement",
            "tests/test_ur_robot.py::TestURRobotJointMovement"
        ])
        print("Running movement tests only")
    elif args.cartesian:
        pytest_args.append("tests/test_ur_robot.py::TestURRobotCartesianMovement")
        print("Running cartesian movement tests only")
    elif args.joint:
        pytest_args.append("tests/test_ur_robot.py::TestURRobotJointMovement")
        print("Running joint movement tests only")
    elif args.utilities:
        pytest_args.append("tests/test_ur_robot.py::TestURRobotUtilities")
        print("Running utility function tests only")
    else:
        # Default: run all tests
        print("Running all tests")
        # Check robot availability for information
        if check_robot_connection(args.robot_ip):
            print(f"✓ Robot at {args.robot_ip} is available")
        else:
            print(f"✗ Robot at {args.robot_ip} is not available. Robot-dependent tests will be skipped.")
    
    # Run the tests
    result = run_pytest(pytest_args)
    return result.returncode


if __name__ == "__main__":
    sys.exit(main())
