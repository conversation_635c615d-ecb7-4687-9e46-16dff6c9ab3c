# robot protocol

from typing import Protocol
from dataclasses import dataclass


@dataclass
class Pose3D:
    x: float
    y: float
    z: float
    rx: float
    ry: float
    rz: float


@dataclass
class Joints:
    j1: float
    j2: float
    j3: float
    j4: float
    j5: float
    j6: float


class Robot(Protocol):
    def movel(self, pose: Pose3D, a: float, v: float, block: bool = True) -> None: ...

    def movej(self, joints: Joints, a: float, v: float, block: bool = True) -> None: ...

    def movel_relative(
        self,
        pose_delta: Pose3D,
        a: float,
        v: float,
        block: bool = True,
    ) -> None: ...

    def movej_relative(
        self,
        joints_delta: Joints,
        a: float,
        v: float,
        block: bool = True,
    ) -> None: ...

    def get_pose(self) -> Pose3D: ...

    def get_joints(self) -> Joints: ...

    def get_joint_velocities(self) -> list[float]: ...

    def is_moving(self) -> bool: ...

    def wait_until_stopped(self) -> None: ...
