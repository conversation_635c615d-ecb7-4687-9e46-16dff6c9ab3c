"""
Comprehensive unit tests for UR robot functionality.

This test suite covers:
- Connection tests (primary and real-time data ports)
- Data reading and parsing tests (TCP pose, joint positions, joint velocities)
- Cartesian movement tests (X, Y, Z axis movements)
- Joint movement tests (individual joint movements)

Tests include proper setup/teardown, error handling, and tolerance verification.
"""

import pytest
import socket
import math
import struct
from unittest.mock import Mock, patch, MagicMock
from robot.ur.ur_robot import URRobot
from robot.robot import Pose3D, Joints


# Test configuration
ROBOT_IP = "***************"
PRIMARY_PORT = 30001
SECONDARY_PORT = 30002
POSITION_TOLERANCE = 0.001  # 1mm tolerance for cartesian movements
JOINT_TOLERANCE = math.radians(0.1)  # 0.1 degree tolerance for joint movements
RETURN_TOLERANCE = 0.002  # 2mm tolerance for returning to original position
JOINT_RETURN_TOLERANCE = math.radians(0.2)  # 0.2 degree tolerance for returning to original joints


def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line(
        "markers", "urrobot: mark test as requiring actual UR robot connection"
    )


def check_robot_connection(robot_ip: str = ROBOT_IP, port: int = PRIMARY_PORT) -> bool:
    """
    Check if robot is available for connection.

    Args:
        robot_ip: IP address of the robot
        port: Port to test connection on

    Returns:
        True if connection successful, False otherwise
    """
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as test_socket:
            test_socket.settimeout(2.0)
            test_socket.connect((robot_ip, port))
            return True
    except (socket.error, socket.timeout):
        return False


@pytest.fixture(scope="session")
def robot_available():
    """Session-scoped fixture to check if robot is available."""
    return check_robot_connection()


@pytest.fixture
def ur_robot():
    """Fixture providing a URRobot instance."""
    return URRobot(robot_ip=ROBOT_IP)


@pytest.fixture
def mock_socket():
    """Fixture providing a mock socket for testing without actual robot connection."""
    with patch("socket.socket") as mock_socket_class:
        mock_socket_instance = MagicMock()
        mock_socket_class.return_value.__enter__.return_value = mock_socket_instance
        yield mock_socket_instance


class TestURRobotConnection:
    """Test suite for robot connection functionality."""

    @pytest.mark.urrobot
    def test_primary_port_connection(self, ur_robot, robot_available):
        """Test successful connection on the primary control port."""
        if not robot_available:
            pytest.skip("Robot not available for connection testing")

        # Test connection by sending a simple command
        try:
            # This should not raise an exception if connection is successful
            ur_robot._send_urscript_command("# test connection")
        except Exception as e:
            pytest.fail(f"Failed to connect to primary port {PRIMARY_PORT}: {e}")

    @pytest.mark.urrobot
    def test_secondary_port_connection(self, ur_robot, robot_available):
        """Test successful connection on the real-time data port."""
        if not robot_available:
            pytest.skip("Robot not available for connection testing")

        # Test connection by reading robot state
        try:
            # This should not raise an exception if connection is successful
            data = ur_robot._read_robot_state()
            assert len(data) > 0, "Should receive robot state data"
        except Exception as e:
            pytest.fail(f"Failed to connect to secondary port {SECONDARY_PORT}: {e}")

    def test_connection_timeout_handling(self, ur_robot, mock_socket):
        """Test proper handling of connection timeouts."""
        # Configure mock to raise timeout
        mock_socket.connect.side_effect = socket.timeout("Connection timeout")

        with pytest.raises(RuntimeError, match="Failed to send command"):
            ur_robot._send_urscript_command("# test timeout")

    def test_connection_error_handling(self, ur_robot, mock_socket):
        """Test proper handling of connection errors."""
        # Configure mock to raise connection error
        mock_socket.connect.side_effect = socket.error("Connection refused")

        with pytest.raises(RuntimeError, match="Failed to send command"):
            ur_robot._send_urscript_command("# test error")


class TestURRobotDataParsing:
    """Test suite for robot data reading and parsing functionality."""

    @pytest.mark.urrobot
    def test_tcp_pose_reading_and_parsing(self, ur_robot, robot_available):
        """Test reading and parsing robot state data using TCP pose method."""
        if not robot_available:
            pytest.skip("Robot not available for data parsing testing")

        # Read and parse TCP pose
        pose = ur_robot.get_pose()

        # Verify pose contains expected fields and data types
        assert isinstance(pose, Pose3D), "Should return Pose3D object"
        assert isinstance(pose.x, float), "X coordinate should be float"
        assert isinstance(pose.y, float), "Y coordinate should be float"
        assert isinstance(pose.z, float), "Z coordinate should be float"
        assert isinstance(pose.rx, float), "RX rotation should be float"
        assert isinstance(pose.ry, float), "RY rotation should be float"
        assert isinstance(pose.rz, float), "RZ rotation should be float"

        # Verify reasonable pose values (robot workspace bounds)
        assert -2.0 <= pose.x <= 2.0, f"X coordinate {pose.x} outside reasonable range"
        assert -2.0 <= pose.y <= 2.0, f"Y coordinate {pose.y} outside reasonable range"
        assert -2.0 <= pose.z <= 2.0, f"Z coordinate {pose.z} outside reasonable range"
        assert -math.pi <= pose.rx <= math.pi, f"RX rotation {pose.rx} outside range"
        assert -math.pi <= pose.ry <= math.pi, f"RY rotation {pose.ry} outside range"
        assert -math.pi <= pose.rz <= math.pi, f"RZ rotation {pose.rz} outside range"

    @pytest.mark.urrobot
    def test_joint_positions_reading_and_parsing(self, ur_robot, robot_available):
        """Test reading and parsing robot state data using joint positions method."""
        if not robot_available:
            pytest.skip("Robot not available for data parsing testing")

        # Read and parse joint positions
        joints = ur_robot.get_joints()

        # Verify joints contains expected fields and data types
        assert isinstance(joints, Joints), "Should return Joints object"
        assert isinstance(joints.j1, float), "Joint 1 should be float"
        assert isinstance(joints.j2, float), "Joint 2 should be float"
        assert isinstance(joints.j3, float), "Joint 3 should be float"
        assert isinstance(joints.j4, float), "Joint 4 should be float"
        assert isinstance(joints.j5, float), "Joint 5 should be float"
        assert isinstance(joints.j6, float), "Joint 6 should be float"

        # Verify reasonable joint values (typical UR joint limits)
        joint_values = [joints.j1, joints.j2, joints.j3, joints.j4, joints.j5, joints.j6]
        for i, joint_val in enumerate(joint_values, 1):
            assert -2 * math.pi <= joint_val <= 2 * math.pi, (
                f"Joint {i} value {joint_val} outside reasonable range"
            )

    @pytest.mark.urrobot
    def test_joint_velocities_reading_and_parsing(self, ur_robot, robot_available):
        """Test reading and parsing robot state data using joint velocities method."""
        if not robot_available:
            pytest.skip("Robot not available for data parsing testing")

        # Read and parse joint velocities
        velocities = ur_robot.get_joint_velocities()

        # Verify velocities contains expected fields and data types
        assert isinstance(velocities, list), "Should return list of velocities"
        assert len(velocities) == 6, "Should return 6 joint velocities"

        for i, velocity in enumerate(velocities):
            assert isinstance(velocity, float), f"Joint {i + 1} velocity should be float"
            # Reasonable velocity range (rad/s) - UR robots typically max ~3 rad/s
            assert -10.0 <= velocity <= 10.0, (
                f"Joint {i + 1} velocity {velocity} outside reasonable range"
            )


class TestURRobotCartesianMovement:
    """Test suite for cartesian movement functionality."""

    @pytest.mark.urrobot
    def test_x_axis_movement(self, ur_robot, robot_available):
        """Test movement in positive X direction with tolerance verification."""
        if not robot_available:
            pytest.skip("Robot not available for movement testing")

        # Read initial TCP position
        initial_pose = ur_robot.get_pose()

        # Move 2 cm in positive X direction
        delta_x = 0.02  # 2 cm
        ur_robot.movel_relative(Pose3D(x=delta_x, y=0, z=0, rx=0, ry=0, rz=0))

        # Read new position
        new_pose = ur_robot.get_pose()

        # Verify movement is within tolerance (±1mm)
        actual_delta_x = new_pose.x - initial_pose.x
        assert abs(actual_delta_x - delta_x) <= POSITION_TOLERANCE, (
            f"X movement error: expected {delta_x}, got {actual_delta_x}, "
            f"error {abs(actual_delta_x - delta_x)} > tolerance {POSITION_TOLERANCE}"
        )

        # Verify other axes didn't move significantly
        assert abs(new_pose.y - initial_pose.y) <= POSITION_TOLERANCE, (
            "Y axis should not move during X movement"
        )
        assert abs(new_pose.z - initial_pose.z) <= POSITION_TOLERANCE, (
            "Z axis should not move during X movement"
        )

        # Move back to original position
        ur_robot.movel(initial_pose)

        # Verify return to original position within 2mm tolerance
        final_pose = ur_robot.get_pose()
        position_error = math.sqrt(
            (final_pose.x - initial_pose.x) ** 2
            + (final_pose.y - initial_pose.y) ** 2
            + (final_pose.z - initial_pose.z) ** 2
        )
        assert position_error <= RETURN_TOLERANCE, (
            f"Failed to return to original position: error {position_error} > {RETURN_TOLERANCE}"
        )

    @pytest.mark.urrobot
    def test_y_axis_movement(self, ur_robot, robot_available):
        """Test movement in positive Y direction with tolerance verification."""
        if not robot_available:
            pytest.skip("Robot not available for movement testing")

        # Read initial TCP position
        initial_pose = ur_robot.get_pose()

        # Move 2 cm in positive Y direction
        delta_y = 0.02  # 2 cm
        ur_robot.movel_relative(Pose3D(x=0, y=delta_y, z=0, rx=0, ry=0, rz=0))

        # Read new position
        new_pose = ur_robot.get_pose()

        # Verify movement is within tolerance (±1mm)
        actual_delta_y = new_pose.y - initial_pose.y
        assert abs(actual_delta_y - delta_y) <= POSITION_TOLERANCE, (
            f"Y movement error: expected {delta_y}, got {actual_delta_y}, "
            f"error {abs(actual_delta_y - delta_y)} > tolerance {POSITION_TOLERANCE}"
        )

        # Verify other axes didn't move significantly
        assert abs(new_pose.x - initial_pose.x) <= POSITION_TOLERANCE, (
            "X axis should not move during Y movement"
        )
        assert abs(new_pose.z - initial_pose.z) <= POSITION_TOLERANCE, (
            "Z axis should not move during Y movement"
        )

        # Move back to original position
        ur_robot.movel(initial_pose)

        # Verify return to original position within 2mm tolerance
        final_pose = ur_robot.get_pose()
        position_error = math.sqrt(
            (final_pose.x - initial_pose.x) ** 2
            + (final_pose.y - initial_pose.y) ** 2
            + (final_pose.z - initial_pose.z) ** 2
        )
        assert position_error <= RETURN_TOLERANCE, (
            f"Failed to return to original position: error {position_error} > {RETURN_TOLERANCE}"
        )

    @pytest.mark.urrobot
    def test_z_axis_movement(self, ur_robot, robot_available):
        """Test movement in positive Z direction with tolerance verification."""
        if not robot_available:
            pytest.skip("Robot not available for movement testing")

        # Read initial TCP position
        initial_pose = ur_robot.get_pose()

        # Move 2 cm in positive Z direction
        delta_z = 0.02  # 2 cm
        ur_robot.movel_relative(Pose3D(x=0, y=0, z=delta_z, rx=0, ry=0, rz=0))

        # Read new position
        new_pose = ur_robot.get_pose()

        # Verify movement is within tolerance (±1mm)
        actual_delta_z = new_pose.z - initial_pose.z
        assert abs(actual_delta_z - delta_z) <= POSITION_TOLERANCE, (
            f"Z movement error: expected {delta_z}, got {actual_delta_z}, "
            f"error {abs(actual_delta_z - delta_z)} > tolerance {POSITION_TOLERANCE}"
        )

        # Verify other axes didn't move significantly
        assert abs(new_pose.x - initial_pose.x) <= POSITION_TOLERANCE, (
            "X axis should not move during Z movement"
        )
        assert abs(new_pose.y - initial_pose.y) <= POSITION_TOLERANCE, (
            "Y axis should not move during Z movement"
        )

        # Move back to original position
        ur_robot.movel(initial_pose)

        # Verify return to original position within 2mm tolerance
        final_pose = ur_robot.get_pose()
        position_error = math.sqrt(
            (final_pose.x - initial_pose.x) ** 2
            + (final_pose.y - initial_pose.y) ** 2
            + (final_pose.z - initial_pose.z) ** 2
        )
        assert position_error <= RETURN_TOLERANCE, (
            f"Failed to return to original position: error {position_error} > {RETURN_TOLERANCE}"
        )


class TestURRobotJointMovement:
    """Test suite for joint movement functionality."""

    @pytest.mark.urrobot
    def test_joint1_movement(self, ur_robot, robot_available):
        """Test movement of joint 1 by +2 degrees with tolerance verification."""
        if not robot_available:
            pytest.skip("Robot not available for movement testing")

        # Read initial joint positions
        initial_joints = ur_robot.get_joints()

        # Move joint 1 by +2 degrees
        delta_j1 = math.radians(2.0)  # 2 degrees in radians
        target_joints = Joints(
            j1=initial_joints.j1 + delta_j1,
            j2=initial_joints.j2,
            j3=initial_joints.j3,
            j4=initial_joints.j4,
            j5=initial_joints.j5,
            j6=initial_joints.j6,
        )
        ur_robot.movej(target_joints)

        # Read new joint positions
        new_joints = ur_robot.get_joints()

        # Verify movement is within tolerance (±0.1 degrees)
        actual_delta_j1 = new_joints.j1 - initial_joints.j1
        assert abs(actual_delta_j1 - delta_j1) <= JOINT_TOLERANCE, (
            f"Joint 1 movement error: expected {delta_j1}, got {actual_delta_j1}, "
            f"error {abs(actual_delta_j1 - delta_j1)} > tolerance {JOINT_TOLERANCE}"
        )

        # Verify other joints didn't move significantly
        assert abs(new_joints.j2 - initial_joints.j2) <= JOINT_TOLERANCE, (
            "Joint 2 should not move during joint 1 movement"
        )
        assert abs(new_joints.j3 - initial_joints.j3) <= JOINT_TOLERANCE, (
            "Joint 3 should not move during joint 1 movement"
        )
        assert abs(new_joints.j4 - initial_joints.j4) <= JOINT_TOLERANCE, (
            "Joint 4 should not move during joint 1 movement"
        )
        assert abs(new_joints.j5 - initial_joints.j5) <= JOINT_TOLERANCE, (
            "Joint 5 should not move during joint 1 movement"
        )
        assert abs(new_joints.j6 - initial_joints.j6) <= JOINT_TOLERANCE, (
            "Joint 6 should not move during joint 1 movement"
        )

        # Move back to original position
        ur_robot.movej(initial_joints)

        # Verify return to original position within 0.2 degrees tolerance
        final_joints = ur_robot.get_joints()
        joint_errors = [
            abs(final_joints.j1 - initial_joints.j1),
            abs(final_joints.j2 - initial_joints.j2),
            abs(final_joints.j3 - initial_joints.j3),
            abs(final_joints.j4 - initial_joints.j4),
            abs(final_joints.j5 - initial_joints.j5),
            abs(final_joints.j6 - initial_joints.j6),
        ]
        max_error = max(joint_errors)
        assert max_error <= JOINT_RETURN_TOLERANCE, (
            f"Failed to return to original joint configuration: max error {max_error} > {JOINT_RETURN_TOLERANCE}"
        )

    @pytest.mark.urrobot
    def test_joint2_movement(self, ur_robot, robot_available):
        """Test movement of joint 2 by +2 degrees with tolerance verification."""
        if not robot_available:
            pytest.skip("Robot not available for movement testing")

        # Read initial joint positions
        initial_joints = ur_robot.get_joints()

        # Move joint 2 by +2 degrees
        delta_j2 = math.radians(2.0)  # 2 degrees in radians
        target_joints = Joints(
            j1=initial_joints.j1,
            j2=initial_joints.j2 + delta_j2,
            j3=initial_joints.j3,
            j4=initial_joints.j4,
            j5=initial_joints.j5,
            j6=initial_joints.j6,
        )
        ur_robot.movej(target_joints)

        # Read new joint positions
        new_joints = ur_robot.get_joints()

        # Verify movement is within tolerance (±0.1 degrees)
        actual_delta_j2 = new_joints.j2 - initial_joints.j2
        assert abs(actual_delta_j2 - delta_j2) <= JOINT_TOLERANCE, (
            f"Joint 2 movement error: expected {delta_j2}, got {actual_delta_j2}, "
            f"error {abs(actual_delta_j2 - delta_j2)} > tolerance {JOINT_TOLERANCE}"
        )

        # Move back to original position
        ur_robot.movej(initial_joints)

        # Verify return to original position within 0.2 degrees tolerance
        final_joints = ur_robot.get_joints()
        joint_errors = [
            abs(final_joints.j1 - initial_joints.j1),
            abs(final_joints.j2 - initial_joints.j2),
            abs(final_joints.j3 - initial_joints.j3),
            abs(final_joints.j4 - initial_joints.j4),
            abs(final_joints.j5 - initial_joints.j5),
            abs(final_joints.j6 - initial_joints.j6),
        ]
        max_error = max(joint_errors)
        assert max_error <= JOINT_RETURN_TOLERANCE, (
            f"Failed to return to original joint configuration: max error {max_error} > {JOINT_RETURN_TOLERANCE}"
        )

    @pytest.mark.urrobot
    def test_joint3_movement(self, ur_robot, robot_available):
        """Test movement of joint 3 by +2 degrees with tolerance verification."""
        if not robot_available:
            pytest.skip("Robot not available for movement testing")

        # Read initial joint positions
        initial_joints = ur_robot.get_joints()

        # Move joint 3 by +2 degrees
        delta_j3 = math.radians(2.0)  # 2 degrees in radians
        target_joints = Joints(
            j1=initial_joints.j1,
            j2=initial_joints.j2,
            j3=initial_joints.j3 + delta_j3,
            j4=initial_joints.j4,
            j5=initial_joints.j5,
            j6=initial_joints.j6,
        )
        ur_robot.movej(target_joints)

        # Read new joint positions
        new_joints = ur_robot.get_joints()

        # Verify movement is within tolerance (±0.1 degrees)
        actual_delta_j3 = new_joints.j3 - initial_joints.j3
        assert abs(actual_delta_j3 - delta_j3) <= JOINT_TOLERANCE, (
            f"Joint 3 movement error: expected {delta_j3}, got {actual_delta_j3}, "
            f"error {abs(actual_delta_j3 - delta_j3)} > tolerance {JOINT_TOLERANCE}"
        )

        # Move back to original position
        ur_robot.movej(initial_joints)

        # Verify return to original position within 0.2 degrees tolerance
        final_joints = ur_robot.get_joints()
        joint_errors = [
            abs(final_joints.j1 - initial_joints.j1),
            abs(final_joints.j2 - initial_joints.j2),
            abs(final_joints.j3 - initial_joints.j3),
            abs(final_joints.j4 - initial_joints.j4),
            abs(final_joints.j5 - initial_joints.j5),
            abs(final_joints.j6 - initial_joints.j6),
        ]
        max_error = max(joint_errors)
        assert max_error <= JOINT_RETURN_TOLERANCE, (
            f"Failed to return to original joint configuration: max error {max_error} > {JOINT_RETURN_TOLERANCE}"
        )

    @pytest.mark.urrobot
    def test_joint4_movement(self, ur_robot, robot_available):
        """Test movement of joint 4 by +2 degrees with tolerance verification."""
        if not robot_available:
            pytest.skip("Robot not available for movement testing")

        # Read initial joint positions
        initial_joints = ur_robot.get_joints()

        # Move joint 4 by +2 degrees
        delta_j4 = math.radians(2.0)  # 2 degrees in radians
        target_joints = Joints(
            j1=initial_joints.j1,
            j2=initial_joints.j2,
            j3=initial_joints.j3,
            j4=initial_joints.j4 + delta_j4,
            j5=initial_joints.j5,
            j6=initial_joints.j6,
        )
        ur_robot.movej(target_joints)

        # Read new joint positions
        new_joints = ur_robot.get_joints()

        # Verify movement is within tolerance (±0.1 degrees)
        actual_delta_j4 = new_joints.j4 - initial_joints.j4
        assert abs(actual_delta_j4 - delta_j4) <= JOINT_TOLERANCE, (
            f"Joint 4 movement error: expected {delta_j4}, got {actual_delta_j4}, "
            f"error {abs(actual_delta_j4 - delta_j4)} > tolerance {JOINT_TOLERANCE}"
        )

        # Move back to original position
        ur_robot.movej(initial_joints)

        # Verify return to original position within 0.2 degrees tolerance
        final_joints = ur_robot.get_joints()
        joint_errors = [
            abs(final_joints.j1 - initial_joints.j1),
            abs(final_joints.j2 - initial_joints.j2),
            abs(final_joints.j3 - initial_joints.j3),
            abs(final_joints.j4 - initial_joints.j4),
            abs(final_joints.j5 - initial_joints.j5),
            abs(final_joints.j6 - initial_joints.j6),
        ]
        max_error = max(joint_errors)
        assert max_error <= JOINT_RETURN_TOLERANCE, (
            f"Failed to return to original joint configuration: max error {max_error} > {JOINT_RETURN_TOLERANCE}"
        )

    @pytest.mark.urrobot
    def test_joint5_movement(self, ur_robot, robot_available):
        """Test movement of joint 5 by +2 degrees with tolerance verification."""
        if not robot_available:
            pytest.skip("Robot not available for movement testing")

        # Read initial joint positions
        initial_joints = ur_robot.get_joints()

        # Move joint 5 by +2 degrees
        delta_j5 = math.radians(2.0)  # 2 degrees in radians
        target_joints = Joints(
            j1=initial_joints.j1,
            j2=initial_joints.j2,
            j3=initial_joints.j3,
            j4=initial_joints.j4,
            j5=initial_joints.j5 + delta_j5,
            j6=initial_joints.j6,
        )
        ur_robot.movej(target_joints)

        # Read new joint positions
        new_joints = ur_robot.get_joints()

        # Verify movement is within tolerance (±0.1 degrees)
        actual_delta_j5 = new_joints.j5 - initial_joints.j5
        assert abs(actual_delta_j5 - delta_j5) <= JOINT_TOLERANCE, (
            f"Joint 5 movement error: expected {delta_j5}, got {actual_delta_j5}, "
            f"error {abs(actual_delta_j5 - delta_j5)} > tolerance {JOINT_TOLERANCE}"
        )

        # Move back to original position
        ur_robot.movej(initial_joints)

        # Verify return to original position within 0.2 degrees tolerance
        final_joints = ur_robot.get_joints()
        joint_errors = [
            abs(final_joints.j1 - initial_joints.j1),
            abs(final_joints.j2 - initial_joints.j2),
            abs(final_joints.j3 - initial_joints.j3),
            abs(final_joints.j4 - initial_joints.j4),
            abs(final_joints.j5 - initial_joints.j5),
            abs(final_joints.j6 - initial_joints.j6),
        ]
        max_error = max(joint_errors)
        assert max_error <= JOINT_RETURN_TOLERANCE, (
            f"Failed to return to original joint configuration: max error {max_error} > {JOINT_RETURN_TOLERANCE}"
        )

    @pytest.mark.urrobot
    def test_joint6_movement(self, ur_robot, robot_available):
        """Test movement of joint 6 by +2 degrees with tolerance verification."""
        if not robot_available:
            pytest.skip("Robot not available for movement testing")

        # Read initial joint positions
        initial_joints = ur_robot.get_joints()

        # Move joint 6 by +2 degrees
        delta_j6 = math.radians(2.0)  # 2 degrees in radians
        target_joints = Joints(
            j1=initial_joints.j1,
            j2=initial_joints.j2,
            j3=initial_joints.j3,
            j4=initial_joints.j4,
            j5=initial_joints.j5,
            j6=initial_joints.j6 + delta_j6,
        )
        ur_robot.movej(target_joints)

        # Read new joint positions
        new_joints = ur_robot.get_joints()

        # Verify movement is within tolerance (±0.1 degrees)
        actual_delta_j6 = new_joints.j6 - initial_joints.j6
        assert abs(actual_delta_j6 - delta_j6) <= JOINT_TOLERANCE, (
            f"Joint 6 movement error: expected {delta_j6}, got {actual_delta_j6}, "
            f"error {abs(actual_delta_j6 - delta_j6)} > tolerance {JOINT_TOLERANCE}"
        )

        # Move back to original position
        ur_robot.movej(initial_joints)

        # Verify return to original position within 0.2 degrees tolerance
        final_joints = ur_robot.get_joints()
        joint_errors = [
            abs(final_joints.j1 - initial_joints.j1),
            abs(final_joints.j2 - initial_joints.j2),
            abs(final_joints.j3 - initial_joints.j3),
            abs(final_joints.j4 - initial_joints.j4),
            abs(final_joints.j5 - initial_joints.j5),
            abs(final_joints.j6 - initial_joints.j6),
        ]
        max_error = max(joint_errors)
        assert max_error <= JOINT_RETURN_TOLERANCE, (
            f"Failed to return to original joint configuration: max error {max_error} > {JOINT_RETURN_TOLERANCE}"
        )


class TestURRobotUtilities:
    """Test suite for robot utility functions."""

    @pytest.mark.urrobot
    def test_is_moving_when_stationary(self, ur_robot, robot_available):
        """Test is_moving returns False when robot is stationary."""
        if not robot_available:
            pytest.skip("Robot not available for utility testing")

        # Ensure robot is stopped
        ur_robot.wait_until_stopped()

        # Check that robot is not moving
        assert not ur_robot.is_moving(), "Robot should not be moving when stationary"

    @pytest.mark.urrobot
    def test_wait_until_stopped(self, ur_robot, robot_available):
        """Test wait_until_stopped function."""
        if not robot_available:
            pytest.skip("Robot not available for utility testing")

        # Start a movement
        current_pose = ur_robot.get_pose()
        ur_robot.movel_relative(Pose3D(x=0.01, y=0, z=0, rx=0, ry=0, rz=0), block=False)

        # Wait until stopped
        ur_robot.wait_until_stopped()

        # Verify robot is not moving
        assert not ur_robot.is_moving(), "Robot should be stopped after wait_until_stopped"

        # Return to original position
        ur_robot.movel(current_pose)

    def test_robot_initialization(self):
        """Test robot initialization with default and custom parameters."""
        # Test default initialization
        robot1 = URRobot()
        assert robot1.robot_ip == "***************"
        assert robot1.primary_port == 30001
        assert robot1.secondary_port == 30002
        assert robot1.default_acceleration == 1.2
        assert robot1.default_velocity == 0.25

        # Test custom initialization
        robot2 = URRobot(robot_ip="*************", default_acceleration=2.0, default_velocity=0.5)
        assert robot2.robot_ip == "*************"
        assert robot2.default_acceleration == 2.0
        assert robot2.default_velocity == 0.5


class TestURRobotMockTests:
    """Test suite for robot functionality using mocks (no actual robot required)."""

    def test_send_urscript_command_success(self, ur_robot, mock_socket):
        """Test successful URScript command sending."""
        # Configure mock to simulate successful connection and response
        mock_socket.recv.return_value = b"Response data"

        # This should not raise an exception
        ur_robot._send_urscript_command("movel(p[0.1, 0.2, 0.3, 0, 0, 0], a=1.2, v=0.25)")

        # Verify socket operations were called
        mock_socket.connect.assert_called_once_with((ROBOT_IP, PRIMARY_PORT))
        mock_socket.sendall.assert_called_once()
        mock_socket.recv.assert_called_once()

    def test_parse_tcp_pose_with_mock_data(self, ur_robot):
        """Test TCP pose parsing with mock robot state data."""
        # Create mock robot state data with cartesian info package
        # This is a simplified version of actual UR robot state data
        mock_data = bytearray(1000)  # Large enough buffer

        # Message type (robot state = 16)
        mock_data[4] = 16

        # Add cartesian info package at offset 5
        offset = 5
        package_size = 53  # Size of cartesian package
        package_type = 4  # Cartesian info package type

        # Write package header
        mock_data[offset : offset + 4] = struct.pack(">I", package_size)
        mock_data[offset + 4] = package_type

        # Write TCP pose data (6 doubles starting at offset 5 within package)
        pose_offset = offset + 5
        test_pose = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6]  # x, y, z, rx, ry, rz
        for i, val in enumerate(test_pose):
            struct.pack_into(">d", mock_data, pose_offset + i * 8, val)

        # Parse the mock data
        parsed_pose = ur_robot._parse_tcp_pose(bytes(mock_data))

        # Verify parsed values
        assert abs(parsed_pose.x - 0.1) < 1e-10
        assert abs(parsed_pose.y - 0.2) < 1e-10
        assert abs(parsed_pose.z - 0.3) < 1e-10
        assert abs(parsed_pose.rx - 0.4) < 1e-10
        assert abs(parsed_pose.ry - 0.5) < 1e-10
        assert abs(parsed_pose.rz - 0.6) < 1e-10

    def test_parse_joint_positions_with_mock_data(self, ur_robot):
        """Test joint positions parsing with mock robot state data."""
        # Create mock robot state data with joint data package
        mock_data = bytearray(1000)  # Large enough buffer

        # Message type (robot state = 16)
        mock_data[4] = 16

        # Add joint data package at offset 5
        offset = 5
        package_size = 251  # Size of joint package (5 + 6 * 41)
        package_type = 1  # Joint data package type

        # Write package header
        mock_data[offset : offset + 4] = struct.pack(">I", package_size)
        mock_data[offset + 4] = package_type

        # Write joint data (6 joints, each 41 bytes, only first 8 bytes are q_actual)
        joint_data_start = offset + 5
        test_joints = [0.1, 0.2, 0.3, 0.4, 0.5, 0.6]  # j1-j6 in radians

        for i, joint_val in enumerate(test_joints):
            joint_offset = joint_data_start + i * 41
            struct.pack_into(">d", mock_data, joint_offset, joint_val)

        # Parse the mock data
        parsed_joints = ur_robot._parse_joint_positions(bytes(mock_data))

        # Verify parsed values
        assert abs(parsed_joints.j1 - 0.1) < 1e-10
        assert abs(parsed_joints.j2 - 0.2) < 1e-10
        assert abs(parsed_joints.j3 - 0.3) < 1e-10
        assert abs(parsed_joints.j4 - 0.4) < 1e-10
        assert abs(parsed_joints.j5 - 0.5) < 1e-10
        assert abs(parsed_joints.j6 - 0.6) < 1e-10

    def test_parse_joint_velocities_with_mock_data(self, ur_robot):
        """Test joint velocities parsing with mock robot state data."""
        # Create mock robot state data with joint data package
        mock_data = bytearray(1000)  # Large enough buffer

        # Message type (robot state = 16)
        mock_data[4] = 16

        # Add joint data package at offset 5
        offset = 5
        package_size = 251  # Size of joint package (5 + 6 * 41)
        package_type = 1  # Joint data package type

        # Write package header
        mock_data[offset : offset + 4] = struct.pack(">I", package_size)
        mock_data[offset + 4] = package_type

        # Write joint data (6 joints, each 41 bytes)
        # qd_actual (velocity) is at offset 16 within each joint
        joint_data_start = offset + 5
        test_velocities = [0.01, 0.02, 0.03, 0.04, 0.05, 0.06]  # rad/s

        for i, velocity in enumerate(test_velocities):
            joint_offset = joint_data_start + i * 41
            velocity_offset = joint_offset + 16  # Skip q_actual (8) + q_target (8)
            struct.pack_into(">d", mock_data, velocity_offset, velocity)

        # Parse the mock data
        parsed_velocities = ur_robot._parse_joint_velocities(bytes(mock_data))

        # Verify parsed values
        assert len(parsed_velocities) == 6
        for i, expected_vel in enumerate(test_velocities):
            assert abs(parsed_velocities[i] - expected_vel) < 1e-10

    def test_parse_tcp_pose_missing_cartesian_package(self, ur_robot):
        """Test TCP pose parsing error when cartesian package is missing."""
        # Create mock data without cartesian info package
        mock_data = bytearray(100)
        mock_data[4] = 16  # Robot state message type

        # Add a different package type (not cartesian)
        offset = 5
        mock_data[offset : offset + 4] = struct.pack(">I", 20)  # Package size
        mock_data[offset + 4] = 2  # Different package type (not 4)

        with pytest.raises(RuntimeError, match="Cartesian info not found"):
            ur_robot._parse_tcp_pose(bytes(mock_data))

    def test_parse_joint_positions_missing_joint_package(self, ur_robot):
        """Test joint positions parsing error when joint package is missing."""
        # Create mock data without joint data package
        mock_data = bytearray(100)
        mock_data[4] = 16  # Robot state message type

        # Add a different package type (not joint)
        offset = 5
        mock_data[offset : offset + 4] = struct.pack(">I", 20)  # Package size
        mock_data[offset + 4] = 3  # Different package type (not 1)

        with pytest.raises(RuntimeError, match="Joint data not found"):
            ur_robot._parse_joint_positions(bytes(mock_data))

    def test_is_moving_with_mock_velocities(self, ur_robot):
        """Test is_moving function with mocked joint velocities."""
        # Test with stationary robot (all velocities below threshold)
        with patch.object(
            ur_robot,
            "get_joint_velocities",
            return_value=[0.0001, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001],
        ):
            assert not ur_robot.is_moving()

        # Test with moving robot (one velocity above threshold)
        with patch.object(
            ur_robot,
            "get_joint_velocities",
            return_value=[0.002, 0.0001, 0.0001, 0.0001, 0.0001, 0.0001],
        ):
            assert ur_robot.is_moving()

        # Test with exception (should return True for safety)
        with patch.object(
            ur_robot, "get_joint_velocities", side_effect=Exception("Connection error")
        ):
            assert ur_robot.is_moving()


class TestURRobotParametrizedTests:
    """Test suite using parametrized tests to reduce code duplication."""

    @pytest.mark.parametrize(
        "joint_index,joint_name",
        [(0, "j1"), (1, "j2"), (2, "j3"), (3, "j4"), (4, "j5"), (5, "j6")],
    )
    @pytest.mark.urrobot
    def test_joint_movement_parametrized(self, ur_robot, robot_available, joint_index, joint_name):
        """Parametrized test for individual joint movements."""
        if not robot_available:
            pytest.skip("Robot not available for parametrized movement testing")

        # Read initial joint positions
        initial_joints = ur_robot.get_joints()
        initial_values = [
            initial_joints.j1,
            initial_joints.j2,
            initial_joints.j3,
            initial_joints.j4,
            initial_joints.j5,
            initial_joints.j6,
        ]

        # Create target joints with only one joint moved
        delta = math.radians(2.0)  # 2 degrees
        target_values = initial_values.copy()
        target_values[joint_index] += delta

        target_joints = Joints(
            j1=target_values[0],
            j2=target_values[1],
            j3=target_values[2],
            j4=target_values[3],
            j5=target_values[4],
            j6=target_values[5],
        )

        # Move joint
        ur_robot.movej(target_joints)

        # Read new joint positions
        new_joints = ur_robot.get_joints()
        new_values = [
            new_joints.j1,
            new_joints.j2,
            new_joints.j3,
            new_joints.j4,
            new_joints.j5,
            new_joints.j6,
        ]

        # Verify movement is within tolerance
        actual_delta = new_values[joint_index] - initial_values[joint_index]
        assert abs(actual_delta - delta) <= JOINT_TOLERANCE, (
            f"{joint_name} movement error: expected {delta}, got {actual_delta}"
        )

        # Move back to original position
        ur_robot.movej(initial_joints)

    @pytest.mark.parametrize("axis,axis_name", [(0, "x"), (1, "y"), (2, "z")])
    @pytest.mark.urrobot
    def test_cartesian_movement_parametrized(self, ur_robot, robot_available, axis, axis_name):
        """Parametrized test for cartesian axis movements."""
        if not robot_available:
            pytest.skip("Robot not available for parametrized cartesian testing")

        # Read initial TCP position
        initial_pose = ur_robot.get_pose()

        # Create movement delta for specified axis
        delta = 0.02  # 2 cm
        delta_pose = Pose3D(x=0, y=0, z=0, rx=0, ry=0, rz=0)
        if axis == 0:
            delta_pose.x = delta
        elif axis == 1:
            delta_pose.y = delta
        elif axis == 2:
            delta_pose.z = delta

        # Move in specified axis
        ur_robot.movel_relative(delta_pose)

        # Read new position
        new_pose = ur_robot.get_pose()

        # Verify movement is within tolerance
        initial_values = [initial_pose.x, initial_pose.y, initial_pose.z]
        new_values = [new_pose.x, new_pose.y, new_pose.z]
        actual_delta = new_values[axis] - initial_values[axis]

        assert abs(actual_delta - delta) <= POSITION_TOLERANCE, (
            f"{axis_name} movement error: expected {delta}, got {actual_delta}"
        )

        # Move back to original position
        ur_robot.movel(initial_pose)
