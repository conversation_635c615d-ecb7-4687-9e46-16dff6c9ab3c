import socket
import struct

from robot.robot import Robot, Pose3D, Joints


class URRobot(Robot):
    """
    Universal Robot implementation of the Robot protocol.

    This class provides control and monitoring capabilities for UR robots
    using socket communication and URScript commands. Each method call
    creates its own socket connection for simplicity and robustness.
    """

    def __init__(
        self,
        robot_ip: str = "***************",
        default_acceleration: float = 1.2,
        default_velocity: float = 0.25,
    ):
        """
        Initialize the UR robot.

        Args:
            robot_ip: IP address of the UR robot (default: "***************")
        """
        self.robot_ip = robot_ip
        self.primary_port = 30001  # For sending commands
        self.secondary_port = 30002  # For reading state
        self.default_acceleration = default_acceleration  # m/s² for movel, rad/s² for movej
        self.default_velocity = default_velocity  # m/s for movel, rad/s for movej

    def movel(self, pose: Pose3D, a: float = None, v: float = None, block: bool = True) -> None:
        """
        Move the robot to a pose using linear movement.

        Args:
            pose: Target pose (x, y, z, rx, ry, rz)
        """
        # Convert pose to URScript format
        pose_list = [pose.x, pose.y, pose.z, pose.rx, pose.ry, pose.rz]

        # Use default acceleration and velocity if not provided
        if a is None:
            a = self.default_acceleration
        if v is None:
            v = self.default_velocity

        # Create URScript movel command
        command = f"movel(p{pose_list}, a={a}, v={v})"

        self._send_urscript_command(command)
        if block:
            self.wait_until_stopped()

    def movej(self, joints: Joints, a: float = None, v: float = None, block: bool = True) -> None:
        """
        Move the robot to joint positions.

        Args:
            joints: Target joint positions in radians
        """
        # Convert joints to URScript format
        joint_list = [joints.j1, joints.j2, joints.j3, joints.j4, joints.j5, joints.j6]

        # Use default acceleration and velocity if not provided
        if a is None:
            a = self.default_acceleration
        if v is None:
            v = self.default_velocity

        # Create URScript movej command
        command = f"movej({joint_list}, a={a}, v={v})"

        self._send_urscript_command(command)
        if block:
            self.wait_until_stopped()

    def movel_relative(
        self,
        pose_delta: Pose3D,
        a: float = None,
        v: float = None,
        block: bool = True,
    ) -> None:
        """
        Move the robot relative to current pose using linear movement.

        Args:
            pose_delta: Relative pose change (dx, dy, dz, drx, dry, drz)
        """
        # Get current pose
        current_pose = self.get_pose()

        # Calculate target pose by adding delta
        target_pose = Pose3D(
            x=current_pose.x + pose_delta.x,
            y=current_pose.y + pose_delta.y,
            z=current_pose.z + pose_delta.z,
            rx=current_pose.rx + pose_delta.rx,
            ry=current_pose.ry + pose_delta.ry,
            rz=current_pose.rz + pose_delta.rz,
        )

        # Execute absolute movement to target pose
        self.movel(target_pose, a, v, block)

    def movej_relative(
        self,
        joints_delta: Joints,
        a: float = None,
        v: float = None,
        block: bool = True,
    ) -> None:
        """
        Move the robot relative to current joint positions.

        Args:
            joints_delta: Relative joint changes (dj1, dj2, dj3, dj4, dj5, dj6)
        """
        # Get current joint positions
        current_joints = self.get_joints()

        # Calculate target joints by adding delta
        target_joints = Joints(
            j1=current_joints.j1 + joints_delta.j1,
            j2=current_joints.j2 + joints_delta.j2,
            j3=current_joints.j3 + joints_delta.j3,
            j4=current_joints.j4 + joints_delta.j4,
            j5=current_joints.j5 + joints_delta.j5,
            j6=current_joints.j6 + joints_delta.j6,
        )

        # Execute absolute movement to target joints
        self.movej(target_joints, a, v, block)

    def get_pose(self) -> Pose3D:
        """
        Get current TCP pose.

        Returns:
            Current TCP pose (x, y, z, rx, ry, rz)
        """
        data = self._read_robot_state()
        return self._parse_tcp_pose(data)

    def get_joints(self) -> Joints:
        """
        Get current joint positions.

        Returns:
            Current joint positions in radians
        """
        data = self._read_robot_state()
        return self._parse_joint_positions(data)

    def get_joint_velocities(self) -> list[float]:
        """
        Get current joint velocities from robot state data.

        Returns:
            List of 6 joint velocities in rad/s
        """
        data = self._read_robot_state()
        return self._parse_joint_velocities(data)

    def is_moving(self, velocity_threshold: float = 0.001) -> bool:
        """
        Check if the robot is currently moving.

        Args:
            velocity_threshold: Minimum velocity to consider as moving (rad/s)

        Returns:
            True if the robot is moving, False otherwise
        """
        try:
            # Get joint velocities from robot state
            joint_velocities = self.get_joint_velocities()

            # Check if any joint is moving above threshold
            for velocity in joint_velocities:
                if abs(velocity) > velocity_threshold:
                    return True

            return False

        except Exception:
            # If we can't read velocities, assume robot might be moving for safety
            return True

    def wait_until_stopped(self) -> None:
        """
        Wait until the robot has stopped moving.
        """
        while self.is_moving():
            pass

    def _send_urscript_command(self, command: str) -> None:
        """
        Send a URScript command to the robot.

        Args:
            command: URScript command to send
        """
        try:
            # Create socket connection
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as cmd_socket:
                cmd_socket.settimeout(10.0)  # 10 second timeout
                cmd_socket.connect((self.robot_ip, self.primary_port))

                # URScript commands must end with double newline
                command_with_newline = command + "\n\n"

                # Send the command
                cmd_socket.sendall(command_with_newline.encode("utf-8"))

                # Read response (at least 79 bytes as per documentation)
                _ = cmd_socket.recv(1024)  # Read and discard response

            print(f"Command sent successfully: {command}")

        except Exception as e:
            raise RuntimeError(f"Failed to send command '{command}': {e}")

    def _read_robot_state(self) -> bytes:
        """
        Read robot state data from the secondary port.

        Returns:
            Raw robot state data as bytes
        """
        try:
            # Connect to secondary port for reading state
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as state_socket:
                state_socket.settimeout(10.0)  # 10 second timeout
                state_socket.connect((self.robot_ip, self.secondary_port))

                # Read multiple messages until we find a robot state message (type 16)
                for _ in range(10):  # Try up to 10 messages
                    # Read the message size first (4 bytes, big-endian integer)
                    size_data = state_socket.recv(4)
                    if len(size_data) != 4:
                        raise RuntimeError("Failed to read message size")

                    message_size = struct.unpack(">I", size_data)[0]

                    # Read the rest of the message
                    remaining_data = state_socket.recv(message_size - 4)

                    full_data = size_data + remaining_data

                    # Check if this is a robot state message (type 16)
                    if len(full_data) >= 5:
                        message_type = struct.unpack(">B", full_data[4:5])[0]
                        if message_type == 16:  # Robot state message
                            return full_data

                    # If not robot state, continue to next message
                    # (Skip version messages, safety messages, etc.)

                raise RuntimeError("No robot state message found after 10 attempts")

        except Exception as e:
            raise RuntimeError(f"Failed to read robot state: {e}")

    def _parse_tcp_pose(self, data: bytes) -> Pose3D:
        """
        Parse TCP pose from robot state data.

        Args:
            data: Raw robot state data

        Returns:
            Current TCP pose
        """
        try:
            # Skip to cartesian info section
            offset = 5  # Skip message type

            # Find cartesian info package (type 4)
            while offset < len(data) - 8:
                package_size = struct.unpack(">I", data[offset : offset + 4])[0]
                package_type = struct.unpack(">B", data[offset + 4 : offset + 5])[0]

                if package_type == 4:  # Cartesian info package
                    # TCP pose starts at offset 5 within the cartesian package
                    pose_offset = offset + 5

                    # Read 6 pose values (8 bytes each, double precision)
                    pose_data = []
                    for i in range(6):
                        pose_val = struct.unpack(
                            ">d", data[pose_offset + i * 8 : pose_offset + (i + 1) * 8]
                        )[0]
                        pose_data.append(pose_val)

                    return Pose3D(
                        x=pose_data[0],
                        y=pose_data[1],
                        z=pose_data[2],
                        rx=pose_data[3],
                        ry=pose_data[4],
                        rz=pose_data[5],
                    )

                offset += package_size

            raise RuntimeError("Cartesian info not found in robot state")

        except Exception as e:
            raise RuntimeError(f"Failed to parse TCP pose: {e}")

    def _parse_joint_positions(self, data: bytes) -> Joints:
        """
        Parse joint positions from robot state data.

        Args:
            data: Raw robot state data

        Returns:
            Current joint positions
        """
        try:
            # Skip to joint data section
            # The joint data starts after the robot mode data
            # Robot mode data is typically at offset 5 (1 byte message type + 4 bytes size)
            # Joint data package starts after robot mode data package

            offset = 5  # Skip message type

            # Find joint data package (type 1)
            while offset < len(data) - 8:
                package_size = struct.unpack(">I", data[offset : offset + 4])[0]
                package_type = struct.unpack(">B", data[offset + 4 : offset + 5])[0]

                if package_type == 1:  # Joint data package
                    # Joint data starts at offset 5 within the joint package (after size + type)
                    joint_data_start = offset + 5

                    # Each joint has: q_actual(8) + q_target(8) + qd_actual(8) + I_actual(4) + V_actual(4) + T_motor(4) + T_micro(4) + jointMode(1) = 41 bytes
                    joint_size = 41

                    # Read 6 joint positions (only q_actual, the first 8 bytes of each joint)
                    joints_data = []
                    for i in range(6):
                        joint_offset = joint_data_start + i * joint_size
                        # Read only q_actual (first 8 bytes of each joint)
                        joint_pos = struct.unpack(
                            ">d",
                            data[joint_offset : joint_offset + 8],
                        )[0]
                        joints_data.append(joint_pos)

                    return Joints(
                        j1=joints_data[0],
                        j2=joints_data[1],
                        j3=joints_data[2],
                        j4=joints_data[3],
                        j5=joints_data[4],
                        j6=joints_data[5],
                    )

                offset += package_size

            raise RuntimeError("Joint data not found in robot state")

        except Exception as e:
            raise RuntimeError(f"Failed to parse joint positions: {e}")

    def _parse_joint_velocities(self, data: bytes) -> list[float]:
        """
        Parse joint velocities from robot state data.

        Args:
            data: Raw robot state data

        Returns:
            List of current joint velocities in rad/s
        """
        try:
            # Skip to joint data section
            offset = 5  # Skip message type

            # Find joint data package (type 1)
            while offset < len(data) - 8:
                package_size = struct.unpack(">I", data[offset : offset + 4])[0]
                package_type = struct.unpack(">B", data[offset + 4 : offset + 5])[0]

                if package_type == 1:  # Joint data package
                    # Joint data starts at offset 5 within the joint package (after size + type)
                    joint_data_start = offset + 5

                    # Each joint has: q_actual(8) + q_target(8) + qd_actual(8) + I_actual(4) + V_actual(4) + T_motor(4) + T_micro(4) + jointMode(1) = 41 bytes
                    joint_size = 41

                    # Read 6 joint velocities (qd_actual, which is at offset 16 within each joint)
                    velocities = []
                    for i in range(6):
                        joint_offset = joint_data_start + i * joint_size
                        # Skip q_actual (8 bytes) and q_target (8 bytes) to get to qd_actual
                        velocity_offset = joint_offset + 16
                        velocity = struct.unpack(
                            ">d",
                            data[velocity_offset : velocity_offset + 8],
                        )[0]
                        velocities.append(velocity)

                    return velocities

                offset += package_size

            raise RuntimeError("Joint data not found in robot state")

        except Exception as e:
            raise RuntimeError(f"Failed to parse joint velocities: {e}")


if __name__ == "__main__":
    robot = URRobot()
    print(robot.get_joints())
    print(robot.get_pose())
    robot.movel_relative(Pose3D(x=0.2, y=0.0, z=0.0, rx=0.0, ry=0.0, rz=0.0))
    robot.movel_relative(Pose3D(x=0.0, y=0.2, z=0.0, rx=0.0, ry=0.0, rz=0.0))
    robot.movel_relative(Pose3D(x=0.0, y=0.0, z=0.2, rx=0.0, ry=0.0, rz=0.0))
    robot.movel_relative(Pose3D(x=-0.2, y=0.0, z=0.0, rx=0.0, ry=0.0, rz=0.0))
    robot.movel_relative(Pose3D(x=0.0, y=-0.2, z=0.0, rx=0.0, ry=0.0, rz=0.0))
    robot.movel_relative(Pose3D(x=0.0, y=0.0, z=-0.2, rx=0.0, ry=0.0, rz=0.0))
