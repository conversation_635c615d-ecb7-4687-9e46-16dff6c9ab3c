["tests/test_ur_robot.py::TestURRobotConnection::test_connection_error_handling", "tests/test_ur_robot.py::TestURRobotConnection::test_connection_timeout_handling", "tests/test_ur_robot.py::TestURRobotMockTests::test_is_moving_with_mock_velocities", "tests/test_ur_robot.py::TestURRobotMockTests::test_parse_joint_positions_missing_joint_package", "tests/test_ur_robot.py::TestURRobotMockTests::test_parse_joint_positions_with_mock_data", "tests/test_ur_robot.py::TestURRobotMockTests::test_parse_joint_velocities_with_mock_data", "tests/test_ur_robot.py::TestURRobotMockTests::test_parse_tcp_pose_missing_cartesian_package", "tests/test_ur_robot.py::TestURRobotMockTests::test_parse_tcp_pose_with_mock_data", "tests/test_ur_robot.py::TestURRobotMockTests::test_send_urscript_command_success", "tests/test_ur_robot.py::TestURRobotParametrizedTests::test_cartesian_movement_parametrized[0-x]", "tests/test_ur_robot.py::TestURRobotParametrizedTests::test_cartesian_movement_parametrized[1-y]", "tests/test_ur_robot.py::TestURRobotParametrizedTests::test_cartesian_movement_parametrized[2-z]", "tests/test_ur_robot.py::TestURRobotParametrizedTests::test_joint_movement_parametrized[0-j1]", "tests/test_ur_robot.py::TestURRobotParametrizedTests::test_joint_movement_parametrized[1-j2]", "tests/test_ur_robot.py::TestURRobotParametrizedTests::test_joint_movement_parametrized[2-j3]", "tests/test_ur_robot.py::TestURRobotParametrizedTests::test_joint_movement_parametrized[3-j4]", "tests/test_ur_robot.py::TestURRobotParametrizedTests::test_joint_movement_parametrized[4-j5]", "tests/test_ur_robot.py::TestURRobotParametrizedTests::test_joint_movement_parametrized[5-j6]", "tests/test_ur_robot.py::TestURRobotUtilities::test_robot_initialization"]