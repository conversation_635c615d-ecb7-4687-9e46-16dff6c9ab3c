# UR Robot Test Suite

This directory contains comprehensive unit tests for the UR robot functionality in `robot/ur/ur_robot.py`.

## Test Structure

The test suite is organized into several test classes:

### 1. TestURRobotConnection
- Tests connection to primary control port (30001)
- Tests connection to real-time data port (30002)
- Tests connection timeout and error handling

### 2. TestURRobotDataParsing
- Tests reading and parsing TCP pose data
- Tests reading and parsing joint positions data
- Tests reading and parsing joint velocities data
- Validates data types and reasonable value ranges

### 3. TestURRobotCartesianMovement
- Tests movement in X, Y, and Z directions (2cm each)
- Verifies movement accuracy within ±1mm tolerance
- Tests return to original position within ±2mm tolerance
- Each axis movement is a separate test method

### 4. TestURRobotJointMovement
- Tests individual joint movements (joints 1-6, +2 degrees each)
- Verifies movement accuracy within ±0.1 degree tolerance
- Tests return to original position within ±0.2 degree tolerance
- Each joint movement is a separate test method

### 5. TestURRobotUtilities
- Tests `is_moving()` function when robot is stationary
- Tests `wait_until_stopped()` function
- Tests robot initialization with default and custom parameters

### 6. TestURRobotMockTests
- Tests functionality using mocks (no actual robot required)
- Tests data parsing with mock robot state data
- Tests error handling for missing data packages
- Tests URScript command sending

### 7. TestURRobotParametrizedTests
- Parametrized tests to reduce code duplication
- Tests all joint movements in a single parametrized test
- Tests all cartesian movements in a single parametrized test

## Running the Tests

### Prerequisites
1. Install dependencies using Pipenv:
   ```bash
   pipenv install
   ```

2. Activate the virtual environment:
   ```bash
   pipenv shell
   ```

### Test Execution Options

#### 1. Run All Tests (including robot-dependent tests)
```bash
pytest tests/test_ur_robot.py -v
```

#### 2. Run Only Mock Tests (no robot required)
```bash
pytest tests/test_ur_robot.py -v -m "not urrobot"
```

#### 3. Run Only Robot-Dependent Tests
```bash
pytest tests/test_ur_robot.py -v -m "urrobot"
```

#### 4. Run Specific Test Classes
```bash
# Connection tests only
pytest tests/test_ur_robot.py::TestURRobotConnection -v

# Data parsing tests only
pytest tests/test_ur_robot.py::TestURRobotDataParsing -v

# Movement tests only
pytest tests/test_ur_robot.py::TestURRobotCartesianMovement -v
pytest tests/test_ur_robot.py::TestURRobotJointMovement -v
```

#### 5. Run Specific Test Methods
```bash
# Test X-axis movement only
pytest tests/test_ur_robot.py::TestURRobotCartesianMovement::test_x_axis_movement -v

# Test joint 1 movement only
pytest tests/test_ur_robot.py::TestURRobotJointMovement::test_joint1_movement -v
```

## Test Markers

The test suite uses pytest markers to categorize tests:

- `@pytest.mark.urrobot`: Tests that require an actual UR robot connection
- Tests without this marker can run with mocked data

## Robot Connection Requirements

Tests marked with `@pytest.mark.urrobot` require:
1. A UR robot accessible at IP address `***************` (configurable in test file)
2. Robot should be powered on and in a safe position
3. Robot should have sufficient workspace for 2cm movements in each direction
4. Robot should be in a position where 2-degree joint movements are safe

## Safety Considerations

⚠️ **IMPORTANT SAFETY NOTES:**

1. **Robot Position**: Ensure the robot is in a safe position before running movement tests
2. **Workspace**: Verify the robot has sufficient workspace for movements
3. **Emergency Stop**: Keep emergency stop accessible during testing
4. **Supervision**: Never run robot tests unattended
5. **Test Environment**: Run tests in a controlled environment away from people and obstacles

## Test Configuration

Key test parameters can be modified in `tests/test_ur_robot.py`:

```python
ROBOT_IP = "***************"          # Robot IP address
PRIMARY_PORT = 30001                   # Primary control port
SECONDARY_PORT = 30002                 # Real-time data port
POSITION_TOLERANCE = 0.001             # 1mm tolerance for cartesian movements
JOINT_TOLERANCE = math.radians(0.1)    # 0.1 degree tolerance for joint movements
RETURN_TOLERANCE = 0.002               # 2mm tolerance for returning to original position
JOINT_RETURN_TOLERANCE = math.radians(0.2)  # 0.2 degree tolerance for joint returns
```

## Troubleshooting

### Robot Not Available
If robot connection tests are skipped with "Robot not available", check:
1. Robot IP address is correct
2. Robot is powered on and network accessible
3. No firewall blocking ports 30001 and 30002
4. Robot is not in protective stop or error state

### Movement Test Failures
If movement tests fail:
1. Check robot is in a safe position with sufficient workspace
2. Verify robot is not near joint limits
3. Ensure robot is properly calibrated
4. Check for any robot errors or warnings

### Parsing Test Failures
If data parsing tests fail:
1. Verify robot firmware version compatibility
2. Check robot state data format hasn't changed
3. Ensure robot is sending valid state data

## Adding New Tests

When adding new tests:
1. Use descriptive test names and docstrings
2. Add `@pytest.mark.urrobot` for tests requiring actual robot
3. Include proper setup and teardown
4. Use appropriate tolerance values
5. Add meaningful assertion messages
6. Consider safety implications for movement tests
